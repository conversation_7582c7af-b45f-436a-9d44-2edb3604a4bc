<?php
/**
 * Form display and processing class
 */
class RID_COD_Form {

    /**
     * Constructor
     */
    public function __construct() {
        // Add form to product page
        add_action('woocommerce_single_product_summary', array($this, 'display_form'), 30);

        // Remove Add to Cart button
        add_action('init', array($this, 'remove_default_buttons'));
    }

    /**
     * Get current product ID safely
     *
     * @return int|false Product ID or false if not found
     */
    private function get_current_product_id() {
        // First, check if we're in a shortcode context and product_id is in URL params
        if (isset($_GET['product_id']) && is_numeric($_GET['product_id'])) {
            $product_id = intval($_GET['product_id']);
            if ($product_id > 0 && get_post_type($product_id) === 'product') {
                return $product_id;
            }
        }

        // Try to get from current post
        $product_id = get_the_ID();
        if ($product_id && get_post_type($product_id) === 'product') {
            return $product_id;
        }

        // Try to get from global $product
        global $product;
        if ($product && is_object($product) && method_exists($product, 'get_id')) {
            $product_id = $product->get_id();
            if ($product_id && is_numeric($product_id)) {
                return intval($product_id);
            }
        }

        // Try to get from query vars
        if (is_product()) {
            global $wp_query;
            if (isset($wp_query->post) && is_object($wp_query->post) && $wp_query->post->post_type === 'product') {
                return $wp_query->post->ID;
            }
        }

        // Last resort: try to get from URL
        if (is_product()) {
            global $wp;
            if (isset($wp->query_vars['product'])) {
                $product_slug = $wp->query_vars['product'];
                $product_post = get_page_by_path($product_slug, OBJECT, 'product');
                if ($product_post) {
                    return $product_post->ID;
                }
            }
        }

        return false;
    }

    /**
     * Remove default WooCommerce buttons and quantity elements
     */
    public function remove_default_buttons() {
        // Check if we should hide WooCommerce elements on the current product page
        add_action('wp', array($this, 'conditionally_hide_wc_elements'));

        // Always add CSS to handle both scenarios
        add_action('wp_head', array($this, 'add_conditional_css'));
    }

    /**
     * Conditionally hide WooCommerce elements based on whether RID COD form should show
     */
    public function conditionally_hide_wc_elements() {
        // Only on single product pages
        if (!is_product()) {
            return;
        }

        try {
            // Get product ID safely
            $product_id = $this->get_current_product_id();
            if (!$product_id) {
                return;
            }

            // Check if RID COD form should be shown for this product
            $should_show_rid_form = RID_COD_Product_Meta::should_show_form($product_id);

            if ($should_show_rid_form) {
                // Hide WooCommerce default elements and show RID COD form
                $this->hide_woocommerce_elements();
            } else {
                // Show WooCommerce default elements and don't show RID COD form
                $this->ensure_woocommerce_elements_visible();
            }
        } catch (Exception $e) {
            // Log error and fall back to default behavior (show RID COD form)
            error_log('RID COD: Error in conditionally_hide_wc_elements: ' . $e->getMessage());
            $this->hide_woocommerce_elements();
        }
    }

    /**
     * Hide WooCommerce default elements when RID COD form is shown
     */
    private function hide_woocommerce_elements() {
        // Remove add to cart button
        remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_add_to_cart', 30);
    }

    /**
     * Ensure WooCommerce elements are visible when RID COD form is hidden
     */
    private function ensure_woocommerce_elements_visible() {
        // Make sure WooCommerce add to cart is NOT removed
        // (Don't call remove_action for woocommerce_template_single_add_to_cart)
        // The CSS will handle showing/hiding elements
    }

    /**
     * Display the checkout form
     */
    public function display_form() {
        // Get product ID safely
        $product_id = $this->get_current_product_id();
        if (!$product_id) {
            return;
        }

        // Check if the form should be shown for this product
        if (!RID_COD_Product_Meta::should_show_form($product_id)) {
            // Don't render anything - WooCommerce default elements will be shown instead
            return;
        }

        $this->render_form($product_id);
    }

    /**
     * Shortcode callback
     */
    public function shortcode_callback($atts) {
        $atts = shortcode_atts(array(
            'product_id' => 0, // Changed 'id' to 'product_id'
            'id' => 0, // Keep backward compatibility with 'id' parameter
            'force_show' => 'no', // Allow forcing display even if product setting is 'no'
        ), $atts, 'rid_cod_form');

        // Use product_id if specified, otherwise use id for backward compatibility
        $product_id = !empty($atts['product_id']) ? $atts['product_id'] : $atts['id'];

        // If no product ID specified, try to get from current context
        if (empty($product_id)) {
            $product_id = $this->get_current_product_id();
        }

        // If still no product ID, return error message
        if (empty($product_id)) {
            return '<p style="color: red; border: 1px solid red; padding: 10px; background: #ffe6e6; border-radius: 5px;">' . 
                   __('خطأ في الكود المختصر: يجب تحديد معرف المنتج أو استخدام الكود في صفحة المنتج', 'rid-cod') . 
                   '<br><strong>' . __('مثال صحيح:', 'rid-cod') . '</strong> [rid_cod_form product_id="123"]</p>';
        }

        // Validate that the product exists
        $product = wc_get_product($product_id);
        if (!$product) {
            return '<p style="color: red; border: 1px solid red; padding: 10px; background: #ffe6e6; border-radius: 5px;">' . 
                   sprintf(__('خطأ: المنتج برقم %d غير موجود', 'rid-cod'), $product_id) . '</p>';
        }

        // Check if the form should be shown for this product (unless forced)
        if ($atts['force_show'] !== 'yes' && !RID_COD_Product_Meta::should_show_form($product_id)) {
            // For shortcode usage, show the form by default unless explicitly disabled
            // This allows landing pages to work as expected
            // Only hide if force_show is explicitly set to "no"
            if ($atts['force_show'] === 'no') {
                return '<!-- RID COD form hidden for this product (force_show="no") -->';
            }
            // Default behavior: show form in shortcode even if product setting is disabled
        }

        ob_start();
        $this->render_form($product_id); // Use resolved product_id
        return ob_get_clean();
    }

    /**
     * Render the checkout form
     */
    public function render_form($product_id) {
        $product = wc_get_product($product_id);

        if (!$product) {
            return;
        }

        $product_type = $product->get_type();
        $product_price = $product->get_price();

        // Get Algeria states
        // Get current country and its states
        $current_country = RID_COD_Country_Manager::get_current_country();
        $states = RID_COD_Country_Manager::get_states_by_country($current_country);
        $country_data = RID_COD_Country_Manager::get_country_data($current_country);

        // Get shipping zones and methods defined in WooCommerce
        $wc_shipping_methods = $this->get_wc_shipping_methods();

        // Get default per-state costs from settings
        $default_state_costs = $this->get_default_state_costs();

        // Get delivery type settings
        $enable_delivery_type = get_option('rid_cod_enable_delivery_type', 'no') === 'yes';
        $delivery_type_home_label = get_option('rid_cod_delivery_type_home_label', __('توصيل للمنزل', 'rid-cod'));
        $delivery_type_desk_label = get_option('rid_cod_delivery_type_desk_label', __('توصيل للمكتب', 'rid-cod'));

        // Get WhatsApp button settings
        $enable_whatsapp_button = get_option('rid_cod_enable_whatsapp_button', 'yes') === 'yes';
        $whatsapp_number = get_option('rid_cod_whatsapp_number', '');

        // Get prevent autocomplete setting
        $prevent_autocomplete = get_option('rid_cod_prevent_autocomplete', 'no') === 'yes';
        $autocomplete_attr = $prevent_autocomplete ? ' autocomplete="off"' : '';

        // Get form control settings
        $show_notes = get_option('rid_cod_show_notes', 'no') === 'yes';
        $show_states = get_option('rid_cod_show_states', 'yes') === 'yes';
        $show_cities = get_option('rid_cod_show_cities', 'yes') === 'yes';
        $show_color_names = get_option('rid_cod_show_color_names', 'no') === 'yes';
        $variation_size = get_option('rid_cod_variation_size', 'medium');

        // Get form style setting
        $form_style = get_option('rid_cod_form_style', 'classic');
        $form_class = 'rid-cod-form-classic'; // Default
        if ($form_style === 'modern') {
            $form_class = 'rid-cod-form-modern';
        } elseif ($form_style === 'mobile') {
            $form_class = 'rid-cod-form-mobile';
        }

        // Start output buffering
        ob_start();
        ?>
        <div class="<?php echo esc_attr($form_class); ?>">
        <div id="rid-cod-checkout">
            <div class="rid-cod-title">
                <h3><?php echo esc_html(get_option('rid_cod_form_title', __('للطلب يرجى ملئ الإستمارة أسفله', 'rid-cod'))); ?> <span class="form-title-icon">👇</span></h3>
            </div>

            <form id="rid-cod-form" class="checkout">
                <input type="hidden" name="product_id" value="<?php echo esc_attr($product_id); ?>">
                <input type="hidden" name="nonce" value="<?php echo wp_create_nonce('rid_cod_form_nonce'); ?>">
                <input type="hidden" name="quantity" id="rid-cod-quantity" value="1">
                <input type="hidden" name="shipping_method" id="rid-cod-shipping-method" value="">
                <input type="hidden" name="shipping_cost" id="rid-cod-shipping-cost" value="0">

                <?php // Removed orphaned else/endif block for simple products, as it was moved with the main 'if variable' block ?>

                <div class="rid-cod-customer-info">
                    <div class="rid-cod-field-group rid-cod-field-with-icon">
                        <?php /* <label for="rid-cod-full-name"><?php echo esc_html(get_option('rid_cod_field_name', __('الاسم الكامل', 'rid-cod'))); ?></label> */ ?>
                        <span class="rid-input-icon rid-icon-user"></span>
                        <input type="text" id="rid-cod-full-name" name="full_name" placeholder="<?php echo esc_attr(get_option('rid_cod_field_name', __('الاسم الكامل', 'rid-cod'))); ?>" required<?php echo $autocomplete_attr; ?>>
                    </div>

                    <div class="rid-cod-field-group rid-cod-field-with-icon">
                        <?php /* <label for="rid-cod-phone"><?php echo esc_html(get_option('rid_cod_field_phone', __('رقم الهاتف', 'rid-cod'))); ?></label> */ ?>
                        <span class="rid-input-icon rid-icon-phone"></span>
                        <input type="tel" id="rid-cod-phone" name="phone" placeholder="<?php echo esc_attr(get_option('rid_cod_field_phone', __('رقم الهاتف', 'rid-cod'))); ?>" required<?php echo $autocomplete_attr; ?>>
                    </div>

                    <?php if (!empty($states) && $show_states) : ?>
                        <div class="rid-cod-field-group rid-cod-field-with-icon">
                            <?php /* <label for="rid-cod-state"><?php echo esc_html(get_option('rid_cod_field_state', __('الولاية', 'rid-cod'))); ?></label> */ ?>
                            <span class="rid-input-icon rid-icon-state"></span>
                            <select id="rid-cod-state" name="state"<?php echo $show_states ? ' required' : ''; ?>>
                                <option value="" disabled selected><?php echo esc_html(get_option('rid_cod_field_state', __('الولاية', 'rid-cod'))); ?></option>
                                <?php foreach ($states as $state_code => $state_name) : ?>
                                    <?php $display_text = sprintf('%s - %s', $state_code, $state_name); // Combine code and name ?>
                                    <option value="<?php echo esc_attr($state_code); ?>" data-state="<?php echo esc_attr($state_name); ?>"><?php echo esc_html($display_text); ?></option> <?php // Display combined text ?>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <?php if ($show_cities) : ?>
                        <div class="rid-cod-field-group rid-cod-field-with-icon">
                            <?php /* <label for="rid-cod-city"><?php echo esc_html(get_option('rid_cod_field_city', __('البلدية', 'rid-cod'))); ?></label> */ ?>
                            <span class="rid-input-icon rid-icon-city"></span>
                            <select id="rid-cod-city" name="city"<?php echo $show_cities ? ' required' : ''; ?> disabled>
                                <option value="" disabled selected><?php echo esc_html(get_option('rid_cod_field_city', __('البلدية', 'rid-cod'))); ?></option>
                            </select>
                        </div>
                        <?php endif; ?>
                    <?php else : ?>
                        <?php if ($show_states) : ?>
                        <div class="rid-cod-field-group rid-cod-field-with-icon">
                            <?php /* <label for="rid-cod-state-text"><?php echo esc_html(get_option('rid_cod_field_state', __('الولاية', 'rid-cod'))); ?></label> */ ?>
                            <span class="rid-input-icon rid-icon-state"></span>
                            <input type="text" id="rid-cod-state-text" name="state" placeholder="<?php echo esc_attr(get_option('rid_cod_field_state', __('الولاية', 'rid-cod'))); ?>"<?php echo $show_states ? ' required' : ''; ?><?php echo $autocomplete_attr; ?>>
                        </div>
                        <?php endif; ?>

                        <?php if ($show_cities) : ?>
                        <div class="rid-cod-field-group rid-cod-field-with-icon">
                            <?php /* <label for="rid-cod-city-text"><?php echo esc_html(get_option('rid_cod_field_city', __('البلدية', 'rid-cod'))); ?></label> */ ?>
                            <span class="rid-input-icon rid-icon-city"></span>
                            <input type="text" id="rid-cod-city-text" name="city" placeholder="<?php echo esc_attr(get_option('rid_cod_field_city', __('البلدية', 'rid-cod'))); ?>"<?php echo $show_cities ? ' required' : ''; ?><?php echo $autocomplete_attr; ?>>
                        </div>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php // Delivery Type moved to summary section ?>

                    <?php // Add Notes field if enabled ?>
                    <?php if ($show_notes) : ?>
                    <div class="rid-cod-field-group rid-cod-field-with-icon">
                        <span class="rid-input-icon rid-icon-notes"></span>
                        <textarea id="rid-cod-notes" name="notes" placeholder="<?php echo esc_attr(get_option('rid_cod_field_notes', __('ملاحظات إضافية (اختياري)', 'rid-cod'))); ?>"<?php echo $autocomplete_attr; ?>></textarea>
                    </div>
                    <?php endif; ?>
                </div>

                <?php // --- Variations Section (Using Enhanced WooCommerce Integration) --- ?>
                <?php if ($product_type === 'variable') : ?>
                    <div class="rid-cod-variations rid-cod-field-group" data-title="<?php echo esc_attr(__('اختر خصائص المنتج', 'rid-cod')); ?>">
                        <?php
                        // Get the attributes and available variations for JS
                        $attributes = $product->get_variation_attributes();
                        $available_variations = $product->get_available_variations();

                        // Make sure we have variations data
                        if (empty($available_variations)) {
                            echo '<p class="error">هذا المنتج ليس لديه خيارات متاحة حاليًا.</p>';
                        } else {
                            // Encode the variations data for JavaScript
                            $variations_json = wp_json_encode($available_variations);
                            $variations_attr = function_exists('wc_esc_json') ? wc_esc_json($variations_json) : _wp_specialchars($variations_json, ENT_QUOTES, 'UTF-8', true);

                            // Build a variations container that's compatible with WooCommerce (no nested form)
                            echo '<div class="variations_form cart"';
                            echo ' data-product_id="' . esc_attr($product->get_id()) . '"';
                            echo ' data-product_variations="' . esc_attr($variations_attr) . '">';

                            // Add hidden inputs for WooCommerce compatibility (these will be part of the main form)
                            echo '<input type="hidden" name="wc_variation_id" class="variation_id" value="0" />';

                            // Output the variations in div structure instead of table
                            echo '<div class="variations-container">';

                            // Output each attribute with box-style options
                            foreach ($attributes as $attribute_name => $options) {
                                $attribute = sanitize_title($attribute_name);
                                $attribute_label = wc_attribute_label($attribute_name);
                                // تحسين التعرف على صفات الألوان بإضافة المزيد من المصطلحات باللغة العربية والإنجليزية
                                $color_terms = ['color', 'colour', 'لون', 'الوان', 'ألوان', 'الألوان', 'colors', 'colours', 'colorway', 'hue', 'لوان'];
                                $is_color_attribute = false;
                                foreach ($color_terms as $term) {
                                    if (stripos($attribute_name, $term) !== false) {
                                        $is_color_attribute = true;
                                        break;
                                    }
                                }

                                echo '<div class="variation-row">';
                                echo '<div class="variation-label">';
                                echo '<span class="attribute-label">' . esc_html($attribute_label) . '</span>';

                                // Important: Use attribute_ prefix for WooCommerce compatibility
                                $select_name = 'attribute_' . $attribute;

                                // Output hidden select for form submission - WooCommerce compatible naming
                                echo '<select id="' . esc_attr($attribute) . '" name="' . esc_attr($select_name) . '" class="rid-cod-variation-select"';
                                echo ' data-attribute_name="' . esc_attr($select_name) . '"';
                                // Add decoded attribute label for JavaScript error handling
                                echo ' data-decoded-label="' . esc_attr(rid_cod_decode_variation_attribute($attribute_label)) . '"';
                                echo ' style="display:none;">';
                                echo '<option value="">' . esc_html__('اختر', 'rid-cod') . ' ' . esc_html($attribute_label) . '</option>';

                                // Add each option to the dropdown
                                if (!empty($options)) {
                                    foreach ($options as $option) {
                                        $decoded_option = rid_cod_decode_variation_attribute($option);
                                        echo '<option value="' . esc_attr($option) . '">' . esc_html($decoded_option) . '</option>';
                                    }
                                }
                                echo '</select>';

                                // Box-style variation options
                                echo '<div class="variation-boxes" data-attribute="' . esc_attr($select_name) . '">';

                                if (!empty($options)) {
                                    foreach ($options as $option) {
                                        if ($is_color_attribute) {
                                            // Try to detect if this is a color value
                                            $color_value = $option;

                                            // Use the new helper function to decode and clean the color value
                                            $decoded_color_value = rid_cod_decode_variation_attribute($color_value);
                                            
                                            // Use decoded value for display and processing
                                            $display_value = $decoded_color_value;

                                            // Check if it's a color name that needs to be converted to hex
                                            if (!preg_match('/^#[a-f0-9]{6}$/i', $decoded_color_value)) {
                                                // Use the decoded value for color mapping
                                                $color_key = strtolower($decoded_color_value);
                                                // Common color names to hex (simplified - you can expand this)
                                                $color_map = [
                                                    // الألوان باللغة الإنجليزية
                                                    'red' => '#ff0000',
                                                    'green' => '#00ff00',
                                                    'blue' => '#0000ff',
                                                    'yellow' => '#ffff00',
                                                    'black' => '#000000',
                                                    'white' => '#ffffff',
                                                    'purple' => '#800080',
                                                    'orange' => '#ffa500',
                                                    'brown' => '#a52a2a',
                                                    'pink' => '#ffc0cb',
                                                    'grey' => '#808080',
                                                    'gray' => '#808080',
                                                    'gold' => '#ffd700',
                                                    'silver' => '#c0c0c0',
                                                    'navy' => '#000080',
                                                    'teal' => '#008080',
                                                    'maroon' => '#800000',
                                                    'olive' => '#808000',
                                                    'lime' => '#00ff00',
                                                    'aqua' => '#00ffff',
                                                    'fuchsia' => '#ff00ff',
                                                    'cyan' => '#00ffff',
                                                    'magenta' => '#ff00ff',
                                                    'beige' => '#f5f5dc',
                                                    'tan' => '#d2b48c',
                                                    'khaki' => '#f0e68c',
                                                    // الألوان باللغة الفرنسية
                                                    'rouge' => '#ff0000',
                                                    'vert' => '#00ff00',
                                                    'bleu' => '#0000ff',
                                                    'jaune' => '#ffff00',
                                                    'noir' => '#000000',
                                                    'blanc' => '#ffffff',
                                                    'violet' => '#800080',
                                                    'orange' => '#ffa500',
                                                    'marron' => '#a52a2a',
                                                    'rose' => '#ffc0cb',
                                                    'gris' => '#808080',
                                                    'or' => '#ffd700',
                                                    'argent' => '#c0c0c0',
                                                    'marine' => '#000080',
                                                    'turquoise' => '#40e0d0',
                                                    'bordeaux' => '#800000',
                                                    'olive' => '#808000',
                                                    'citron' => '#ffff00',
                                                    'fuchsia' => '#ff00ff',
                                                    'cyan' => '#00ffff',
                                                    'magenta' => '#ff00ff',
                                                    'beige' => '#f5f5dc',
                                                    'corail' => '#ff7f50',
                                                    'lavande' => '#e6e6fa',
                                                    'saumon' => '#fa8072',
                                                    'emeraude' => '#50c878',
                                                    'indigo' => '#4b0082',
                                                    'bronze' => '#cd7f32',
                                                    'ivoire' => '#fffff0',
                                                    'chocolat' => '#d2691e',
                                                    'prune' => '#dda0dd',
                                                    'cerise' => '#de3163',
                                                    'menthe' => '#98fb98',
                                                    'peche' => '#ffcba4',
                                                    'lilas' => '#c8a2c8',
                                                    'creme' => '#fffdd0',
                                                    'caramel' => '#af6e4d',
                                                    'champagne' => '#f7e7ce',
                                                    'perle' => '#eae0c8',
                                                    'platine' => '#e5e4e2',
                                                    'cuivre' => '#b87333',
                                                    'acier' => '#71797e',
                                                    'charbon' => '#36454f',
                                                    'ecru' => '#c2b280',
                                                    'taupe' => '#483c32',
                                                    'ocre' => '#cc7722',
                                                    'rouille' => '#b7410e',
                                                    'kaki' => '#f0e68c',
                                                    'moutarde' => '#ffdb58',
                                                    'pistache' => '#bef574',
                                                    'anis' => '#9fe855',
                                                    'tilleul' => '#a4c639',
                                                    'celadon' => '#ace1af',
                                                    'jade' => '#00a86b',
                                                    'malachite' => '#0bda51',
                                                    'azur' => '#007fff',
                                                    'cobalt' => '#0047ab',
                                                    'saphir' => '#0f52ba',
                                                    'outremer' => '#4166f5',
                                                    'pervenche' => '#ccccff',
                                                    'mauve' => '#d473d4',
                                                    'parme' => '#cfa0e9',
                                                    'aubergine' => '#370028',
                                                    'grenat' => '#6e0b14',
                                                    'carmin' => '#960018',
                                                    'vermillon' => '#ff4500',
                                                    'ecarlate' => '#ff2400',
                                                    'pourpre' => '#9020a0',
                                                    'magenta' => '#ff00ff',
                                                    'fuchsia' => '#ff77ff',
                                                    'amarante' => '#e52b50',
                                                    'incarnat' => '#ff6b9d',
                                                    'chair' => '#fdbcb4',
                                                    'bisque' => '#ffe4c4',
                                                    'vanille' => '#f3e5ab',
                                                    'paille' => '#ffd700',
                                                    'ambre' => '#ffbf00',
                                                    'topaze' => '#ffc87c',
                                                    'abricot' => '#fbceb1',
                                                    'mandarine' => '#ff8c69',
                                                    'papaye' => '#ffefd5',
                                                    'melon' => '#fdbcb4',
                                                    'mangue' => '#ffcc5c',
                                                    'banane' => '#ffe135',
                                                    'citron' => '#fde910',
                                                    'lime' => '#32cd32',
                                                    'pomme' => '#8db600',
                                                    'avocat' => '#568203',
                                                    'olive' => '#9acd32',
                                                    'sauge' => '#9caf88',
                                                    'eucalyptus' => '#278a5b',
                                                    'pin' => '#01796f',
                                                    'sapin' => '#095228',
                                                    'foret' => '#1b4332',
                                                    'mousse' => '#addfad',
                                                    'lichen' => '#85c17e',
                                                    'algue' => '#7ba05b',
                                                    'ocean' => '#006994',
                                                    'lagon' => '#8fbc8f',
                                                    'glacier' => '#b0e0e6',
                                                    'ciel' => '#87ceeb',
                                                    'ardoise' => '#708090',
                                                    'anthracite' => '#2f4f4f',
                                                    'graphite' => '#41424c',
                                                    'plomb' => '#888888',
                                                    'etain' => '#c0c0c0',
                                                    'zinc' => '#a2a2d0',
                                                    'aluminium' => '#a8a8a8',
                                                    'titane' => '#878681',
                                                    'chrome' => '#c0c0c0',
                                                    'nickel' => '#727472',
                                                    'fer' => '#4c4646',
                                                    // الألوان باللغة العربية
                                                    'أحمر' => '#ff0000',
                                                    'احمر' => '#ff0000',
                                                    'أخضر' => '#00ff00',
                                                    'اخضر' => '#00ff00',
                                                    'أزرق' => '#0000ff',
                                                    'ازرق' => '#0000ff',
                                                    'أصفر' => '#ffff00',
                                                    'اصفر' => '#ffff00',
                                                    'أسود' => '#000000',
                                                    'اسود' => '#000000',
                                                    'أبيض' => '#ffffff',
                                                    'ابيض' => '#ffffff',
                                                    'بنفسجي' => '#800080',
                                                    'برتقالي' => '#ffa500',
                                                    'بني' => '#a52a2a',
                                                    'وردي' => '#ffc0cb',
                                                    'رمادي' => '#808080',
                                                    'ذهبي' => '#ffd700',
                                                    'فضي' => '#c0c0c0',
                                                    'كحلي' => '#000080',
                                                    'بحري' => '#008080',
                                                    'عنابي' => '#800000',
                                                    'زيتوني' => '#808000',
                                                    'فيروزي' => '#40e0d0',
                                                    'ليموني' => '#ffff00',
                                                    'بيج' => '#f5f5dc',
                                                    'كريمي' => '#fffdd0',
                                                    'لؤلؤي' => '#eae0c8',
                                                    'مرجاني' => '#ff7f50',
                                                    'خزامي' => '#e6e6fa',
                                                    'سلموني' => '#fa8072',
                                                    'زمردي' => '#50c878',
                                                    'نيلي' => '#4b0082',
                                                    'برونزي' => '#cd7f32',
                                                    'عاجي' => '#fffff0',
                                                    'شوكولاتي' => '#d2691e',
                                                    'خوخي' => '#ffcba4',
                                                    'نعناعي' => '#98fb98',
                                                    'كراميلي' => '#af6e4d',
                                                    'شامبانيا' => '#f7e7ce',
                                                    'بلاتيني' => '#e5e4e2',
                                                    'نحاسي' => '#b87333',
                                                    'فولاذي' => '#71797e',
                                                    'فحمي' => '#36454f',
                                                    'تراب' => '#c2b280',
                                                    'خردلي' => '#ffdb58',
                                                    'فستقي' => '#bef574',
                                                    'يانسوني' => '#9fe855',
                                                    'سماوي' => '#87ceeb',
                                                    'أردوازي' => '#708090',
                                                    'جرافيتي' => '#41424c',
                                                    'رصاصي' => '#888888',
                                                    'قصديري' => '#c0c0c0',
                                                    'زنكي' => '#a2a2d0',
                                                    'ألمنيومي' => '#a8a8a8',
                                                    'تيتانيومي' => '#878681',
                                                    'كروميومي' => '#c0c0c0',
                                                    'نيكلي' => '#727472',
                                                    'حديدي' => '#4c4646',
                                                ];

                                                // Try to get hex color from map using cleaned value
                                                $hex_color = isset($color_map[$color_key]) ? $color_map[$color_key] : null;
                                                
                                                if (!$hex_color) {
                                                    // Default color for unrecognized names
                                                    $hex_color = '#e9ecef';
                                                    error_log("RID COD Color Not Found: '$color_key' -> Using default");
                                                } else {
                                                    error_log("RID COD Color Found: '$color_key' -> $hex_color");
                                                }
                                                
                                                $color_value = $hex_color;
                                            }

                                            // Color option - show swatch or name based on setting
                                            echo '<div class="variation-option color-option" data-value="' . esc_attr($option) . '" data-decoded-value="' . esc_attr($display_value) . '" title="' . esc_attr($display_value) . '">';
                                            if ($show_color_names) {
                                                // Show color name as text instead of swatch
                                                echo '<span class="color-name">' . esc_html($display_value) . '</span>';
                                            } else {
                                                // Show color swatch (circle) - default behavior
                                                echo '<div class="color-swatch" style="background-color:' . esc_attr($color_value) . '"></div>';
                                            }
                                            echo '</div>';
                                        } else {
                                            // Regular text option with decoded value
                                            $decoded_option = rid_cod_decode_variation_attribute($option);
                                            echo '<div class="variation-option" data-value="' . esc_attr($option) . '" data-decoded-value="' . esc_attr($decoded_option) . '">' . esc_html($decoded_option) . '</div>';
                                        }
                                    }
                                }

                                echo '</div>'; // end .variation-boxes
                                echo '</div>'; // Close variation-label div
                                echo '</div>'; // Close variation-row div
                            }

                            echo '</div>'; // close .variations-container

                            // Reset variations link - standard WooCommerce markup
                            echo '<a class="reset_variations" href="#" style="visibility: hidden;">' . esc_html__('Clear', 'woocommerce') . '</a>';

                            // Standard WooCommerce single variation display
                            echo '<div class="single_variation_wrap">';
                            echo '<div class="woocommerce-variation single_variation"></div>';
                            echo '</div>'; // close .single_variation_wrap

                            echo '</div>'; // close .variations_form (now a div, not a form)
                        }
                        ?>

                        <?php // Hidden inputs for variation data ?>
                        <input type="hidden" name="variation_id" id="rid-cod-variation-id" value="">
                        <input type="hidden" name="variation_price" id="rid-cod-variation-price" value="">

                        <?php if (!empty($available_variations)) : ?>
                        <script type="text/javascript">
                        /* Embed variation data directly after the HTML elements to ensure proper initialization */
                        var rid_cod_variations = <?php echo $variations_json; ?>;
                        jQuery(document).ready(function($) {
                            console.log('RIDCOD: Initializing variations directly');

                            // Initialize WooCommerce variation form
                            $('.rid-cod-variations .variations_form').each(function() {
                                var $form = $(this);

                                // Trigger WooCommerce variation form initialization
                                if (typeof $form.wc_variation_form === 'function') {
                                    $form.wc_variation_form();
                                } else {
                                    // Fallback: trigger the event manually
                                    $form.trigger('wc_variation_form');
                                }
                            });

                            // Also trigger our fallback handlers
                            setTimeout(function() {
                                $('.rid-cod-variation-select').first().trigger('change');
                            }, 100);
                        });
                        </script>
                        <?php endif; ?>
                    </div>
                <?php endif; // End if ($product_type === 'variable') ?>
                <?php // --- End Variations Section --- ?>

                <!-- Quantity and Submit Button Row -->
                <div class="rid-cod-actions-row">
                    <div class="rid-cod-submit">
                        <button type="submit" id="rid-cod-submit-btn"><?php echo esc_html(get_option('rid_cod_button_text', __('انقر هنا لتأكيد الطلب', 'rid-cod'))); ?></button>
                    </div>
                    <div class="rid-cod-quantity">
                        <?php /* <label><?php echo esc_html(get_option('rid_cod_field_quantity', __('الكمية', 'rid-cod'))); ?></label> */ ?>
                        <div class="rid-cod-quantity-selector">
                            <button type="button" id="rid-cod-decrease">-</button>
                            <input type="number" id="rid-cod-quantity-input" value="1" min="1" max="<?php echo esc_attr($product->get_stock_quantity() ? $product->get_stock_quantity() : 99); ?>" readonly>
                            <button type="button" id="rid-cod-increase">+</button>
                        </div>
                    </div>
                </div>

                <?php // WhatsApp Button - Conditionally displayed ?>
                <?php if ($enable_whatsapp_button && !empty($whatsapp_number)) : ?>
                    <?php
                        // Prepare WhatsApp message with product name and link
                        $whatsapp_message = sprintf(
                            __('أريد طلب المنتج: %1$s%2$sرابط المنتج: %3$s', 'rid-cod'),
                            $product->get_name(), // %1$s Product Name
                            "\n",                 // %2$s New line
                            $product->get_permalink() // %3$s Product URL
                        );
                        $whatsapp_url = 'https://wa.me/' . esc_attr($whatsapp_number) . '?text=' . urlencode($whatsapp_message);
                    ?>
                    <div class="rid-cod-whatsapp-button">
                        <a href="<?php echo esc_url($whatsapp_url); ?>" target="_blank" id="rid-cod-whatsapp-btn">
                            <span class="rid-icon-whatsapp"></span> <?php echo esc_html__('أنقر هنا للطلب عبر الواتساب', 'rid-cod'); ?>
                        </a>
                    </div>
                <?php endif; ?>
            </form>

            <!-- Collapsible Order Summary -->
            <div id="rid-cod-summary-wrapper">
                <div id="rid-cod-summary-header">
                    <h4><span class="rid-icon-cart"></span> <?php echo esc_html(get_option('rid_cod_summary_title', __('ملخص الطلب', 'rid-cod'))); ?></h4>
                    <span id="rid-cod-summary-toggle" class="rid-icon-arrow-down"></span>
                </div>
                <div id="rid-cod-summary-content"> <!-- Removed style="display: none;" -->
                    <table>
                        <tr>
                            <td>
                                <span class="product-name"><?php echo esc_html($product->get_name()); ?></span>
                                <div id="rid-cod-summary-variation-details" class="rid-summary-variation-details"></div> <!-- Placeholder for variations -->
                            </td>
                            <td>
                                <span class="rid-summary-quantity">x<span id="rid-cod-product-quantity">1</span></span>
                                <span id="rid-cod-product-price"><?php echo $product_type === 'variable' ? esc_html(__('اختر النوع', 'rid-cod')) : wc_price($product_price); ?></span>
                            </td>
                        </tr>
                        <tr>
                            <td><?php echo esc_html(get_option('rid_cod_shipping_text', __('سعر التوصيل', 'rid-cod'))); ?></td>
                            <td id="rid-cod-shipping-price"><?php echo esc_html(__('إختر الولاية', 'rid-cod')); ?></td>
                        </tr>
                        <?php // Add row for delivery type selection if enabled ?>
                        <?php if ($enable_delivery_type) : ?>
                        <tr id="rid-cod-summary-delivery-type-row">
                            <td><?php echo esc_html(__('نوع التوصيل', 'rid-cod')); ?></td>
                            <td>
                                <div class="summary-delivery-options">
                                    <label class="summary-delivery-option">
                                        <input type="radio" name="delivery_type" value="home" checked>
                                        <span class="summary-radio-custom"></span>
                                        <span class="summary-delivery-text"><?php echo esc_html($delivery_type_home_label); ?></span>
                                    </label>
                                    <label class="summary-delivery-option">
                                        <input type="radio" name="delivery_type" value="desk">
                                        <span class="summary-radio-custom"></span>
                                        <span class="summary-delivery-text"><?php echo esc_html($delivery_type_desk_label); ?></span>
                                    </label>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                        <tr> <?php // Re-add the opening <tr> tag for the total row ?>
                        </tr>
                        <tr class="rid-cod-total">
                            <td><?php echo esc_html(get_option('rid_cod_total_text', __('السعر الإجمالي', 'rid-cod'))); ?></td>
                            <td id="rid-cod-total-price"><?php 
                                if ($product_type === 'variable') {
                                    echo esc_html(__('اختر النوع', 'rid-cod'));
                                } else {
                                    // Use the helper function to format price with selected country currency
                                    echo esc_html(RID_COD_Country_Manager::format_price_with_currency($product_price));
                                }
                            ?></td>
                        </tr>
                    </table>
                </div>
            </div>

            <div id="rid-cod-message"></div>

            <?php // --- Sticky Button HTML (Added Here) ---
            $enable_sticky_button = get_option('rid_cod_enable_sticky_button', 'no') === 'yes';
            if ($enable_sticky_button) :
                $sticky_button_text = get_option('rid_cod_button_text', __('انقر هنا لتأكيد الطلب', 'rid-cod'));
            ?>
            <div id="rid-cod-sticky-button-container" class="rid-cod-sticky-button-container">
                <?php // Use a different ID to avoid conflicts with the main submit button ?>
                <button type="button" id="rid-cod-sticky-submit-btn" class="rid-cod-submit-button"><?php echo esc_html($sticky_button_text); ?></button>
            </div>
            <?php endif; ?>
            <?php // --- End Sticky Button HTML --- ?>

        </div> <?php // End #rid-cod-checkout ?>
        <?php

        // Get shipping methods as JSON for JavaScript
        // Get country manager instance
        if (class_exists('RID_COD_Country_Manager')) {
            $country_manager = new RID_COD_Country_Manager();
            $all_countries_data = $country_manager->get_all_countries_data();
            $phone_patterns = $country_manager->get_all_phone_patterns();
        } else {
            $all_countries_data = array();
            $phone_patterns = array();
        }
        
        // Pass data to JavaScript
        $js_data = array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('rid_cod_nonce'), // General nonce for JS actions
            'select_city' => get_option('rid_cod_select_city_text', __('اختر البلدية', 'rid-cod')),
            'select_variation' => get_option('rid_cod_select_variation_text', __('اختر النوع', 'rid-cod')),
            'select_state' => get_option('rid_cod_select_state_text', __('اختر الولاية', 'rid-cod')),
            'free_shipping' => get_option('rid_cod_free_shipping_text', __('توصيل مجاني', 'rid-cod')),
            'processing' => get_option('rid_cod_processing_text', __('جاري المعالجة...', 'rid-cod')),
            'error' => get_option('rid_cod_error_text', __('حدث خطأ. يرجى المحاولة مرة أخرى.', 'rid-cod')),
            'wc_shipping_methods' => $wc_shipping_methods, // Methods from WC Zones
            'default_state_costs' => $default_state_costs, // Default costs {desk: cost, home: cost} from settings
            'cities_by_state' => $this->get_cities_by_state(), // Cities data
            'current_country' => $current_country, // Current selected country
            'country_data' => $all_countries_data, // All countries data
            'phone_patterns' => $phone_patterns, // All phone patterns
            'enable_delivery_type' => $enable_delivery_type, // Boolean: Is delivery type option enabled?
            'shipping_unavailable' => get_option('rid_cod_shipping_unavailable_text', __('الشحن غير متوفر', 'rid-cod')), // Text for when shipping cost is not found for a selected state
            'enable_phone_validation' => get_option('rid_cod_enable_phone_validation', 'yes') === 'yes', // Pass validation setting to JS
            'prevent_copy_paste' => get_option('rid_cod_prevent_copy_paste', 'no') === 'yes', // Pass copy-paste prevention setting
            'base_product_price' => $product_price, // Pass base product price for simple products
            'enable_sticky_button' => $enable_sticky_button, // Pass sticky button setting to JS
            'show_color_names' => get_option('rid_cod_show_color_names', 'no') === 'yes', // Pass color names setting to JS
            'variation_size' => get_option('rid_cod_variation_size', 'medium'), // Pass variation size setting to JS
            // Pass sticky button colors for potential JS use (though CSS vars are primary)
            'sticky_button_bg_color' => get_option('rid_cod_sticky_button_bg_color', '#6a3de8'),
            'sticky_button_text_color' => get_option('rid_cod_sticky_button_text_color', '#ffffff'),
            'sticky_button_border_color' => get_option('rid_cod_sticky_button_border_color', '#6a3de8'),
            // Form control settings
            'show_states' => get_option('rid_cod_show_states', 'yes') === 'yes',
            'show_cities' => get_option('rid_cod_show_cities', 'yes') === 'yes',
            // General shipping costs (when states are disabled)
            'general_shipping_home' => floatval(get_option('rid_cod_general_shipping_home', 0)),
            'general_shipping_desk' => floatval(get_option('rid_cod_general_shipping_desk', 0)),
        );

        echo '<script type="text/javascript">
            /* <![CDATA[ */
            var rid_cod_params = ' . wp_json_encode($js_data) . ';
            /* ]]> */
        </script>';
        ?>
        </div> <!-- Close form style wrapper -->
        <?php

        echo ob_get_clean();
    }

    /**
     * Get shipping methods defined in WooCommerce Shipping Zones
     */
    private function get_wc_shipping_methods() {
        $transient_key = 'rid_cod_wc_shipping_methods_cache';
        $cached_methods = get_transient($transient_key);

        if (false !== $cached_methods) {
            return $cached_methods;
        }

        $shipping_methods = array();
        $zones = WC_Shipping_Zones::get_zones();

        foreach ($zones as $zone_id => $zone) {
            $zone_obj = new WC_Shipping_Zone($zone['id']);
            $methods = $zone_obj->get_shipping_methods(true);

            foreach ($methods as $method) {
                $method_id = $method->id;
                $method_instance_id = $method->instance_id;
                $method_title = $method->get_title();
                $method_cost = 0;

                if ($method_id === 'flat_rate') {
                    $method_cost = $method->get_option('cost');
                } elseif ($method_id === 'free_shipping') {
                    $method_cost = 0;
                }

                if (!isset($shipping_methods[$zone['zone_name']])) {
                    $shipping_methods[$zone['zone_name']] = array();
                }

                $shipping_methods[$zone['zone_name']][] = array(
                    'id' => $method_instance_id,
                    'method_id' => $method_id,
                    'title' => $method_title,
                    'cost' => $method_cost,
                    'locations' => $zone['zone_locations']
                );
            }
        }

        // Cache for 12 hours (or until manually cleared)
        set_transient($transient_key, $shipping_methods, 12 * HOUR_IN_SECONDS);

        return $shipping_methods;
    }

    /**
     * Get default shipping costs for each state from plugin settings.
     *
     * @return array Associative array with state_code => cost.
     */
    private function get_default_state_costs() {
        $transient_key = 'rid_cod_default_costs_cache';
        $cached_costs = get_transient($transient_key);

        if (false !== $cached_costs) {
            return $cached_costs;
        }

        $current_country = RID_COD_Country_Manager::get_current_country();
        $states = RID_COD_Country_Manager::get_states_by_country($current_country);
        $state_costs = array();

        if (!empty($states)) {
            foreach (array_keys($states) as $state_code) {
                $desk_cost = RID_COD_Shipping_Manager::get_shipping_cost($current_country, $state_code, 'desk');
                $home_cost = RID_COD_Shipping_Manager::get_shipping_cost($current_country, $state_code, 'home');

                $state_costs[$state_code] = [
                    'desk' => $desk_cost !== null ? floatval($desk_cost) : null,
                    'home' => $home_cost !== null ? floatval($home_cost) : null,
                ];
            }
        }

        // Cache for 12 hours (or until manually cleared)
        set_transient($transient_key, $state_costs, 12 * HOUR_IN_SECONDS);

        return $state_costs;
    }


    /**
     * Get cities by state
     */
    private function get_cities_by_state() {
        // Get current country
        $current_country = RID_COD_Country_Manager::get_current_country();
        
        // Return the cities by state from the country manager
        return RID_COD_Country_Manager::get_cities_by_state($current_country);
    }

    /**
     * Add conditional CSS based on current product settings
     */
    public function add_conditional_css() {
        // Only on product pages
        if (!is_product()) {
            return;
        }

        try {
            $product_id = $this->get_current_product_id();
            if (!$product_id) {
                return;
            }

            $should_show_rid_form = RID_COD_Product_Meta::should_show_form($product_id);

            echo '<style id="rid-cod-conditional-css">';

            if ($should_show_rid_form) {
                // RID COD should show - hide WooCommerce elements
                echo '
                    /* Hide WooCommerce elements when RID COD is active */
                    .woocommerce div.product form.cart:not(.rid-cod-variations .variations_form) div.quantity,
                    .woocommerce div.product form.cart:not(.rid-cod-variations .variations_form) .button:not(#rid-cod-submit-btn),
                    .woocommerce div.product .cart:not(#rid-cod-form):not(.rid-cod-variations .variations_form),
                    .woocommerce div.product .woocommerce-variation-add-to-cart,
                    .woocommerce div.product .single_variation_wrap .variations_button,
                    .woocommerce div.product .single_add_to_cart_button,
                    .woocommerce div.product p.cart:not(.rid-cod-variations .variations_form),
                    .woocommerce div.product > form.variations_form.cart .variations,
                    .woocommerce div.product > .single_variation_wrap {
                        display: none !important;
                    }

                    /* Ensure RID COD form is visible */
                    #rid-cod-checkout {
                        display: block !important;
                    }

                    /* Ensure RID COD variation forms are visible */
                    .rid-cod-variations .variations_form,
                    .rid-cod-variations .variations_form .variations,
                    .rid-cod-variations .single_variation_wrap {
                        display: block !important;
                    }
                ';
            } else {
                // RID COD should NOT show - show WooCommerce elements
                echo '
                    /* Show WooCommerce elements when RID COD is hidden */
                    .woocommerce div.product form.cart,
                    .woocommerce div.product form.cart div.quantity,
                    .woocommerce div.product form.cart .button,
                    .woocommerce div.product .cart,
                    .woocommerce div.product .woocommerce-variation-add-to-cart,
                    .woocommerce div.product .single_variation_wrap .variations_button,
                    .woocommerce div.product .single_add_to_cart_button,
                    .woocommerce div.product p.cart,
                    .woocommerce div.product form.variations_form.cart .variations,
                    .woocommerce div.product .single_variation_wrap {
                        display: block !important;
                    }

                    /* Ensure variations work properly */
                    .woocommerce div.product .variations {
                        display: table !important;
                    }

                    .woocommerce div.product .variations tr {
                        display: table-row !important;
                    }

                    .woocommerce div.product .variations td {
                        display: table-cell !important;
                    }

                    /* Hide RID COD form completely */
                    #rid-cod-checkout {
                        display: none !important;
                    }
                ';
            }

            echo '</style>';
        } catch (Exception $e) {
            // Log error and don't output any CSS
            error_log('RID COD: Error in add_conditional_css: ' . $e->getMessage());
        }
    }
}