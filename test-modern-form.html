<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النموذج العصري</title>
    <link rel="stylesheet" href="assets/css/rid-cod.css">
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: #333; margin-bottom: 30px;">اختبار النموذج العصري</h1>
        
        <div class="rid-cod-form-modern">
            <div id="rid-cod-checkout">
                <div class="rid-cod-title">
                    <h3>للطلب أدخل معلوماتك في الخانات أسفله ثم إضغط على زر طلب الآن 👇</h3>
                </div>

                <form id="rid-cod-form" class="checkout">
                    <!-- Customer Info in 2 columns -->
                    <div class="rid-cod-customer-info">
                        <div class="rid-cod-field-group">
                            <input type="text" id="rid-cod-full-name" name="full_name" placeholder="رقم الهاتف" required>
                        </div>

                        <div class="rid-cod-field-group">
                            <input type="tel" id="rid-cod-phone" name="phone" placeholder="الاسم الكامل" required>
                        </div>

                        <div class="rid-cod-field-group">
                            <select id="rid-cod-state" name="state" required>
                                <option value="" disabled>المدينة</option>
                                <option value="adrar" selected>Adrar 01 - أدرار</option>
                                <option value="chlef">Chlef 02 - الشلف</option>
                                <option value="laghouat">Laghouat 03 - الأغواط</option>
                            </select>
                        </div>

                        <div class="rid-cod-field-group">
                            <select id="rid-cod-city" name="city" required>
                                <option value="" disabled>البلدية</option>
                                <option value="adrar-center" selected>Adrar 01 - أدرار</option>
                                <option value="reggane">رقان</option>
                            </select>
                        </div>
                    </div>

                    <!-- Delivery Type Section - Right after customer info -->
                    <div class="rid-cod-delivery-section">
                        <div class="delivery-label">نوع التوصيل:</div>
                        <div class="rid-delivery-options">
                            <label class="delivery-option">
                                <input type="radio" name="delivery_type" value="desk" checked>
                                <span class="radio-custom"></span>
                                <span class="delivery-text">توصيل للمكتب</span>
                            </label>
                            <label class="delivery-option">
                                <input type="radio" name="delivery_type" value="home">
                                <span class="radio-custom"></span>
                                <span class="delivery-text">توصيل للمنزل</span>
                            </label>
                        </div>
                    </div>

                    <!-- Variations Section -->
                    <div class="rid-cod-variations">
                        <div class="variations-container">
                            <div class="variation-row">
                                <div class="variation-label-text">اللون:</div>
                                <div class="variation-boxes" data-attribute="color">
                                    <div class="variation-option color-option" data-value="red" style="background-color: #ff0000;" title="أحمر"></div>
                                    <div class="variation-option color-option" data-value="green" style="background-color: #00ff00;" title="أخضر"></div>
                                    <div class="variation-option color-option selected" data-value="black" style="background-color: #000000;" title="أسود"></div>
                                </div>
                            </div>

                            <div class="variation-row">
                                <div class="variation-label-text">الحجم:</div>
                                <div class="variation-boxes" data-attribute="size">
                                    <div class="variation-option" data-value="xs">XS</div>
                                    <div class="variation-option" data-value="xl">XL</div>
                                    <div class="variation-option" data-value="s">S</div>
                                    <div class="variation-option" data-value="m">M</div>
                                    <div class="variation-option selected" data-value="l">L</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions Row -->
                    <div class="rid-cod-actions-row">
                        <div class="rid-cod-submit">
                            <button type="submit" id="rid-cod-submit-btn">اطلب الآن</button>
                        </div>
                        <div class="rid-cod-quantity">
                            <div class="rid-cod-quantity-selector">
                                <button type="button" id="rid-cod-decrease">-</button>
                                <input type="number" id="rid-cod-quantity-input" value="1" min="1" max="99" readonly>
                                <button type="button" id="rid-cod-increase">+</button>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- Order Summary -->
                <div id="rid-cod-summary-wrapper">
                    <div id="rid-cod-summary-header">
                        <h4><span class="rid-icon-cart"></span> ملخص الطلب</h4>
                        <span id="rid-cod-summary-toggle" class="rid-icon-arrow-down"></span>
                    </div>
                    <div id="rid-cod-summary-content">
                        <table>
                            <tr>
                                <td>
                                    <span class="product-name">منتج التوابل الكويرية</span>
                                    <div class="rid-summary-variation-details">
                                        <span class="variation-detail" id="summary-color">اللون: أسود</span>
                                        <span class="variation-detail" id="summary-size">الحجم: L</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="rid-summary-quantity">x<span id="summary-quantity">1</span></span>
                                    <span id="summary-price">123 دج</span>
                                </td>
                            </tr>
                            <tr>
                                <td id="summary-shipping-label">سعر الشحن</td>
                                <td id="summary-shipping-price">400 دج</td>
                            </tr>
                            <tr id="summary-delivery-type-row">
                                <td>نوع التوصيل</td>
                                <td id="summary-delivery-type">توصيل للمكتب</td>
                            </tr>
                            <tr class="rid-cod-total">
                                <td>السعر الإجمالي</td>
                                <td id="summary-total">523 دج</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Enhanced interactive script
        document.addEventListener('DOMContentLoaded', function() {
            // Color and size name mappings
            const colorNames = {
                'red': 'أحمر',
                'green': 'أخضر',
                'black': 'أسود'
            };

            const sizeNames = {
                'xs': 'XS',
                'xl': 'XL',
                's': 'S',
                'm': 'M',
                'l': 'L'
            };

            // Update summary function
            function updateSummary() {
                // Update quantity
                const quantity = document.getElementById('rid-cod-quantity-input').value;
                document.getElementById('summary-quantity').textContent = quantity;

                // Update selected color
                const selectedColor = document.querySelector('.variation-boxes[data-attribute="color"] .variation-option.selected');
                if (selectedColor) {
                    const colorValue = selectedColor.getAttribute('data-value');
                    document.getElementById('summary-color').textContent = 'اللون: ' + colorNames[colorValue];
                }

                // Update selected size
                const selectedSize = document.querySelector('.variation-boxes[data-attribute="size"] .variation-option.selected');
                if (selectedSize) {
                    const sizeValue = selectedSize.getAttribute('data-value');
                    document.getElementById('summary-size').textContent = 'الحجم: ' + sizeNames[sizeValue];
                }

                // Update delivery type
                const selectedDelivery = document.querySelector('input[name="delivery_type"]:checked');
                if (selectedDelivery) {
                    const deliverySpan = selectedDelivery.parentNode.querySelector('.delivery-text');
                    const deliveryText = deliverySpan ? deliverySpan.textContent.trim() : selectedDelivery.parentNode.textContent.trim();
                    document.getElementById('summary-delivery-type').textContent = deliveryText;
                }
            }

            // Quantity buttons
            const decreaseBtn = document.getElementById('rid-cod-decrease');
            const increaseBtn = document.getElementById('rid-cod-increase');
            const quantityInput = document.getElementById('rid-cod-quantity-input');

            decreaseBtn.addEventListener('click', function() {
                let value = parseInt(quantityInput.value);
                if (value > 1) {
                    quantityInput.value = value - 1;
                    updateSummary();
                }
            });

            increaseBtn.addEventListener('click', function() {
                let value = parseInt(quantityInput.value);
                if (value < 99) {
                    quantityInput.value = value + 1;
                    updateSummary();
                }
            });

            // Variation selection
            document.querySelectorAll('.variation-option').forEach(option => {
                option.addEventListener('click', function() {
                    // Remove selected class from siblings
                    this.parentNode.querySelectorAll('.variation-option').forEach(sibling => {
                        sibling.classList.remove('selected');
                    });
                    // Add selected class to clicked option
                    this.classList.add('selected');
                    updateSummary();
                });
            });

            // Delivery type selection
            document.querySelectorAll('input[name="delivery_type"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    updateSummary();
                });
            });

            // Summary toggle
            const summaryHeader = document.getElementById('rid-cod-summary-header');
            const summaryContent = document.getElementById('rid-cod-summary-content');
            const summaryToggle = document.getElementById('rid-cod-summary-toggle');

            summaryHeader.addEventListener('click', function() {
                if (summaryContent.style.display === 'none') {
                    summaryContent.style.display = 'block';
                    summaryToggle.style.transform = 'rotate(180deg)';
                } else {
                    summaryContent.style.display = 'none';
                    summaryToggle.style.transform = 'rotate(0deg)';
                }
            });

            // Initialize summary
            updateSummary();
        });
    </script>
</body>
</html>
